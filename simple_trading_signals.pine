//@version=5
indicator("Simple Trading Signals", shorttitle="STS", overlay=true)

// ============================================================================
// INPUT PARAMETERS
// ============================================================================

// Moving Average Settings
fast_ma_length = input.int(9, title="Fast MA Length", minval=1, maxval=200)
slow_ma_length = input.int(21, title="Slow MA Length", minval=1, maxval=200)
ma_type = input.string("EMA", title="MA Type", options=["SMA", "EMA"])

// RSI Settings for confirmation
rsi_length = input.int(14, title="RSI Length", minval=1, maxval=50)
rsi_overbought = input.int(70, title="RSI Overbought Level", minval=50, maxval=100)
rsi_oversold = input.int(30, title="RSI Oversold Level", minval=0, maxval=50)

// Signal Display Settings
show_ma = input.bool(true, title="Show Moving Averages")
signal_size = input.string("normal", title="Signal Size", options=["tiny", "small", "normal", "large", "huge"])

// ============================================================================
// CALCULATIONS
// ============================================================================

// Calculate Moving Averages
fast_ma = ma_type == "EMA" ? ta.ema(close, fast_ma_length) : ta.sma(close, fast_ma_length)
slow_ma = ma_type == "EMA" ? ta.ema(close, slow_ma_length) : ta.sma(close, slow_ma_length)

// Calculate RSI for confirmation
rsi = ta.rsi(close, rsi_length)

// ============================================================================
// SIGNAL LOGIC
// ============================================================================

// Moving Average Crossover Conditions
ma_bullish_cross = ta.crossover(fast_ma, slow_ma)
ma_bearish_cross = ta.crossunder(fast_ma, slow_ma)

// RSI Confirmation Conditions
rsi_not_overbought = rsi < rsi_overbought
rsi_not_oversold = rsi > rsi_oversold

// Final Buy and Sell Signals with RSI confirmation
buy_signal = ma_bullish_cross and rsi_not_oversold
sell_signal = ma_bearish_cross and rsi_not_overbought

// ============================================================================
// PLOTTING
// ============================================================================

// Plot Moving Averages (optional)
plot(show_ma ? fast_ma : na, title="Fast MA", color=color.blue, linewidth=2)
plot(show_ma ? slow_ma : na, title="Slow MA", color=color.red, linewidth=2)

// Plot Buy Signals
plotshape(
    series=buy_signal,
    title="Buy Signal",
    location=location.belowbar,
    color=color.green,
    style=shape.triangleup,
    size=signal_size,
    text="BUY"
)

// Plot Sell Signals
plotshape(
    series=sell_signal,
    title="Sell Signal",
    location=location.abovebar,
    color=color.red,
    style=shape.triangledown,
    size=signal_size,
    text="SELL"
)

// ============================================================================
// BACKGROUND COLORING (Optional trend indication)
// ============================================================================

// Color background based on MA trend
trend_up = fast_ma > slow_ma
bgcolor(trend_up ? color.new(color.green, 95) : color.new(color.red, 95), title="Trend Background")

// ============================================================================
// ALERTS
// ============================================================================

// Alert conditions
alertcondition(buy_signal, title="Buy Signal Alert", message="BUY Signal: Fast MA crossed above Slow MA with RSI confirmation")
alertcondition(sell_signal, title="Sell Signal Alert", message="SELL Signal: Fast MA crossed below Slow MA with RSI confirmation")

// Combined alert for any signal
alertcondition(buy_signal or sell_signal, title="Any Signal Alert", message="Trading Signal Generated")

// ============================================================================
// TABLE DISPLAY (Current Status)
// ============================================================================

// Create a table to show current market status
var table info_table = table.new(position.top_right, 2, 4, bgcolor=color.white, border_width=1)

if barstate.islast
    table.cell(info_table, 0, 0, "Fast MA", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, str.tostring(math.round(fast_ma, 2)), text_color=color.black)
    
    table.cell(info_table, 0, 1, "Slow MA", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 1, str.tostring(math.round(slow_ma, 2)), text_color=color.black)
    
    table.cell(info_table, 0, 2, "RSI", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 2, str.tostring(math.round(rsi, 2)), text_color=color.black)
    
    table.cell(info_table, 0, 3, "Trend", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 3, trend_up ? "UP" : "DOWN", text_color=trend_up ? color.green : color.red)

// ============================================================================
// STRATEGY NOTES
// ============================================================================

// This indicator uses:
// 1. Moving Average Crossover: Primary signal generation
//    - BUY when fast MA crosses above slow MA
//    - SELL when fast MA crosses below slow MA
//
// 2. RSI Confirmation: Filters out signals in extreme conditions
//    - Avoids BUY signals when RSI is oversold (potential reversal)
//    - Avoids SELL signals when RSI is overbought (potential reversal)
//
// 3. Visual Elements:
//    - Green triangles below bars for BUY signals
//    - Red triangles above bars for SELL signals
//    - Background color shows overall trend
//    - Information table shows current values
//
// 4. Best Practices:
//    - Use on higher timeframes (15min+) for better reliability
//    - Combine with support/resistance levels
//    - Consider market conditions and volume
//    - Always use proper risk management
